// 如果需要使用antd组件，可以在这里导入
// const { SearchBar, List, Button, Icon } = antd;

class Main extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            searchKeyword: '',
            activeTab: 'operation',
            searchHistory: [],
            allForms: [
                // 运营 - 多区域通用
                {
                    name: '医用封口机检查测试记录表',
                    category: '多区域通用',
                    desc: 'XCWI034-F01A',
                    tab: 'operation',
                    icon: '⚠️'
                },
                // 运营 - 去污
                {
                    name: '清洗消毒器',
                    category: '去污',
                    desc: '清洗消毒器使用记录表',
                    tab: 'operation',
                    icon: '🧼'
                },
                {
                    name: '去污区物表清洁及消毒记录',
                    category: '去污',
                    desc: '去污区物体表面清洁消毒记录表',
                    tab: 'operation',
                    icon: '🧽'
                },
                {
                    name: '（酒精）危化品使用登记表',
                    category: '去污',
                    desc: '（酒精）危化品使用登记表',
                    tab: 'operation',
                    icon: '⚠️'
                },
                {
                    name: '超声清洗机检查及配液配置记录表',
                    category: '去污',
                    desc: 'XCWI030-F01A1',
                    tab: 'operation',
                    icon: '⚠️'
                },
                {
                    name: '煮沸消毒器使用前检查及日常清洁保养表',
                    category: '去污',
                    desc: 'AHWI075-F01/A0',
                    tab: 'operation',
                    icon: '⚠️'
                },
                {
                    name: '紫外线消毒记录',
                    category: '去污',
                    desc: 'AHWI028-F14A1',
                    tab: 'operation',
                    icon: '⚠️'
                },
                {
                    name: '酸化水pH、有效氯检测记录表',
                    category: '去污',
                    desc: 'XCWI040-F01A2-01/02',
                    tab: 'operation',
                    icon: '⚠️'
                },
                {
                    name: '去污区辅助设备清洁及点检记录表',
                    category: '去污',
                    desc: 'XCWI030-F03A',
                    tab: 'operation',
                    icon: '⚠️'
                },
                {
                    name: '清洗层架检查记录表',
                    category: '去污',
                    desc: 'XCWI104-F01A',
                    tab: 'operation',
                    icon: '⚠️'
                },
                {
                    name: '内镜工作站使用后清洁消毒登记表',
                    category: '去污',
                    desc: 'XCWI084-F01A0',
                    tab: 'operation',
                    icon: '⚠️'
                },
                {
                    name: '水池清洗剂、除锈剂配置记录表',
                    category: '去污',
                    desc: 'XCWI094-F01A',
                    tab: 'operation',
                    icon: '⚠️'
                },
                // 运营 - 检查包装
                {
                    name: '绝缘检测仪每日点检记录表',
                    category: '检查包装',
                    desc: 'XCWI041-F01A',
                    tab: 'operation',
                    icon: '⚠️'
                },
                {
                    name: '检查包装区设备清洁保养记录',
                    category: '检查包装',
                    desc: 'XCWI009-F05B',
                    tab: 'operation',
                    icon: '⚠️'
                },
                {
                    name: '检查包装区物表清洁及消毒记录',
                    category: '检查包装',
                    desc: 'AHWI028-F03A1',
                    tab: 'operation',
                    icon: '⚠️'
                },
                // 运营 - 敷料
                {
                    name: '敷料区物表清洁及消毒记录',
                    category: '敷料',
                    desc: 'AHWI028-F05/A1',
                    tab: 'operation',
                    icon: '⚠️'
                },
                // 运营 - 灭菌
                {
                    name: '环氧乙烷气罐使用登记表',
                    category: '灭菌',
                    desc: 'XCWI037-F02A',
                    tab: 'operation',
                    icon: '⚠️'
                },
                {
                    name: '低温环氧乙烷灭菌器检查及清洁记录表',
                    category: '灭菌',
                    desc: 'XCWI037-F01/A1',
                    tab: 'operation',
                    icon: '⚠️'
                },
                {
                    name: '灭菌区设备清洁保养记录',
                    category: '灭菌',
                    desc: 'XCWI035-F02B',
                    tab: 'operation',
                    icon: '⚠️'
                },
                {
                    name: '不合格产品包登记表',
                    category: '灭菌',
                    desc: 'XCWI014-F05A',
                    tab: 'operation',
                    icon: '⚠️'
                },
                {
                    name: '无菌发放区物表清洁及消毒记录',
                    category: '灭菌',
                    desc: 'AHWI028-F04A1',
                    tab: 'operation',
                    icon: '⚠️'
                },
                {
                    name: '压力蒸汽灭菌器运行前检查记录',
                    category: '灭菌',
                    desc: 'XCWI014-F01A/F10A',
                    tab: 'operation',
                    icon: '⚠️'
                },
                // 质量 - 每日填写
                {
                    name: '手持式ATP测试记录',
                    category: '每日填写',
                    desc: 'AHWI021-F04B',
                    tab: 'quality',
                    icon: '🧼'
                },
                {
                    name: '去污区预清洗质量抽查记录表',
                    category: '每日填写',
                    desc: 'AHWI074-F01A0',
                    tab: 'quality',
                    icon: '🫗'
                },
                {
                    name: '生产物料检验记录表',
                    category: '每日填写',
                    desc: 'AHWI023-F01A',
                    tab: 'quality',
                    icon: '🔬'
                },
                {
                    name: '外来器械清洗-一检',
                    category: '每日填写',
                    desc: 'AHWI021-F27-2 A1',
                    tab: 'quality',
                    icon: '🚑'
                },
                {
                    name: '外来器械清洗-术后、预清洗',
                    category: '每日填写',
                    desc: 'AHWI021-F27-1 A1',
                    tab: 'quality',
                    icon: '🚑'
                },
                {
                    name: '灭菌装载合格率抽查记录表',
                    category: '每日填写',
                    desc: 'AHWI021-F28A',
                    tab: 'quality',
                    icon: '🚑'
                },
                {
                    name: '布类抽检表',
                    category: '每日填写',
                    desc: '布类抽检表',
                    tab: 'quality',
                    icon: '🚑'
                },
                // 质量 - 每周填写
                {
                    name: '纯化水质量检测记录（申博）',
                    category: '每周填写',
                    desc: 'AHWI021-F02-02 A0',
                    tab: 'quality',
                    icon: '🚑'
                },
                {
                    name: '纯化水质量检测记录（申梁）',
                    category: '每周填写',
                    desc: 'AHWI021-F02-02 A0',
                    tab: 'quality',
                    icon: '🚑'
                },
                {
                    name: '纯化水质量检测记录（申虹）',
                    category: '每周填写',
                    desc: 'AHWI021-F02-02 A0',
                    tab: 'quality',
                    icon: '🚑'
                },
                // 质量 - 每月填写
                {
                    name: '紫外线灯测试卡记录',
                    category: '每月填写',
                    desc: 'AHWI028－F15A',
                    tab: 'quality',
                    icon: '🧼'
                },
                {
                    name: '蛋白残留测试记录',
                    category: '每月填写',
                    desc: 'AHWI021-F05A1',
                    tab: 'quality',
                    icon: '🧼'
                },
                {
                    name: '工作区照度检测记录',
                    category: '每月填写',
                    desc: 'AHWI021-F08 A1',
                    tab: 'quality',
                    icon: '🧼'
                }
            ]
        }
    }

    componentDidMount() {
        this.loadSearchHistory();
    }

    // 加载搜索历史
    loadSearchHistory = () => {
        try {
            const history = localStorage.getItem('searchHistory');
            if (history) {
                this.setState({searchHistory: JSON.parse(history)});
            }
        } catch (error) {
            console.error('加载搜索历史失败:', error);
        }
    }

    // 保存搜索历史
    saveSearchHistory = (keyword) => {
        try {
            let history = [...this.state.searchHistory];
            // 去重：如果关键词已存在，先删除
            const index = history.indexOf(keyword);
            if (index > -1) {
                history.splice(index, 1);
            }
            // 新记录插入到最前面
            history.unshift(keyword);
            // 只保留最近3条
            if (history.length > 3) {
                history = history.slice(0, 3);
            }

            this.setState({searchHistory: history});
            localStorage.setItem('searchHistory', JSON.stringify(history));
        } catch (error) {
            console.error('保存搜索历史失败:', error);
        }
    }

    // 清除搜索历史
    clearSearchHistory = () => {
        this.setState({searchHistory: []});
        localStorage.removeItem('searchHistory');
    }

    // 搜索处理
    handleSearch = (value) => {
        this.setState({searchKeyword: value});
    }

    // Tab切换
    handleTabChange = (tab) => {
        this.setState({activeTab: tab});
    }

    // 表单卡片点击
    handleFormClick = (form) => {
        // 保存到搜索历史
        this.saveSearchHistory(form.name);

        // 这里可以添加导航到具体表单的逻辑
        console.log('点击表单:', form.name);
        // 示例：跳转到表单页面
        // window.location.href = `/form/${form.id}`;
    }

    // 历史记录点击
    handleHistoryClick = (keyword) => {
        this.setState({searchKeyword: keyword});
    }

    // 获取过滤后的表单数据
    getFilteredForms = () => {
        const {allForms, activeTab, searchKeyword} = this.state;
        let filtered = allForms.filter(form => form.tab === activeTab);

        if (searchKeyword) {
            const keyword = searchKeyword.toLowerCase();
            filtered = filtered.filter(form =>
                form.name.toLowerCase().includes(keyword) ||
                form.desc.toLowerCase().includes(keyword)
            );
        }

        return filtered;
    }

    // 按分类分组表单
    getGroupedForms = () => {
        const filtered = this.getFilteredForms();
        const grouped = {};

        filtered.forEach(form => {
            if (!grouped[form.category]) {
                grouped[form.category] = [];
            }
            grouped[form.category].push(form);
        });

        return grouped;
    }

    // 渲染搜索栏
    renderSearchBar = () => {
        const {searchKeyword, searchHistory} = this.state;

        return (
            <div className="search-bar">
                <div className="search-box">
                    <span className="search-icon">🔍</span>
                    <input
                        type="text"
                        placeholder="搜索表单名称或关键词..."
                        value={searchKeyword}
                        onChange={(e) => this.handleSearch(e.target.value)}
                        autoComplete="off"
                    />
                </div>

                {/* 搜索历史 */}
                <div className="search-history-section">
                    <div className="history-header">
                        <span className="history-title">历史搜索</span>
                        {searchHistory.length > 0 && (
                            <button
                                onClick={this.clearSearchHistory}
                                className="clear-all-btn"
                            >
                                🗑️
                            </button>
                        )}
                    </div>

                    <div className="history-tags">
                        {searchHistory.length > 0 ? (
                            <div className="tags-list">
                                {searchHistory.map((keyword, index) => (
                                    <span
                                        key={index}
                                        className="history-tag"
                                        onClick={() => this.handleHistoryClick(keyword)}
                                    >
                                        {keyword}
                                    </span>
                                ))}
                            </div>
                        ) : (
                            <div className="no-history-tip">暂无历史搜索</div>
                        )}
                    </div>
                </div>
            </div>
        );
    }

    // 渲染Tab切换
    renderTabs = () => {
        const {activeTab} = this.state;

        return (
            <div className="tab-container">
                <div
                    className={`tab-item ${activeTab === 'operation' ? 'active' : ''}`}
                    onClick={() => this.handleTabChange('operation')}
                >
                    运营
                </div>
                <div
                    className={`tab-item ${activeTab === 'quality' ? 'active' : ''}`}
                    onClick={() => this.handleTabChange('quality')}
                >
                    质量
                </div>
            </div>
        );
    }

    // 渲染表单卡片
    renderFormCard = (form, index) => {
        return (
            <div
                key={index}
                className="form-card"
                onClick={() => this.handleFormClick(form)}
            >
                <div className="form-icon">{form.icon}</div>
                <h3 className="form-name">{form.name}</h3>
                <p className="form-desc">{form.desc}</p>
            </div>
        );
    }

    // 渲染表单列表
    renderFormList = () => {
        const {searchKeyword} = this.state;
        const groupedForms = this.getGroupedForms();
        const categories = Object.keys(groupedForms);

        if (searchKeyword && categories.length === 0) {
            return (
                <div className="search-matched">
                    <div className="search-matched-title">搜索结果</div>
                    <div className="form-list">
                        <div className="no-data">没有找到匹配的表单</div>
                    </div>
                </div>
            );
        }

        return (
            <div className="original-forms">
                {categories.map(category => (
                    <div key={category}>
                        <h2 className="category-title">{category}</h2>
                        <div className="form-list">
                            {groupedForms[category].map((form, index) =>
                                this.renderFormCard(form, `${category}-${index}`)
                            )}
                        </div>
                    </div>
                ))}
            </div>
        );
    }

    render() {
        return (
            <div className="SD_Page">
                <div className="container">
                    {this.renderSearchBar()}
                    {this.renderTabs()}
                    {this.renderFormList()}
                </div>
            </div>
        )
    }
}
