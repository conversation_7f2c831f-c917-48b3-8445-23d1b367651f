// 如果需要使用antd组件，可以在这里导入
// const { SearchBar, List, Button, Icon } = antd;

class Main extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            searchKeyword: '',
            activeTab: 'operation',
            searchHistory: [],
            datas: {
                tabs: [{
                    key: '001',
                    name: '运营',
                    order: "1",
                    datas: [{
                        category: "多区域通用",
                        order: "1",
                        forms: [{
                            name: '医用封口机检查测试记录表',
                            desc: 'XCWI034-F01A',
                            icon: "327318",
                            url: "/mobilemode/appHomepageView.jsp?appHomepageId=135",
                        }]
                    },
                        {
                            category: "去污",
                            order: "2",
                            forms: [{
                                name: '清洗消毒器',
                                desc: '清洗消毒器使用记录表',
                                icon: "327318",
                                url: "/mobilemode/appHomepageView.jsp?appHomepageId=54",
                            }]
                        },
                        {
                            category: "检查包装",
                            order: "3",
                            forms: [{
                                name: '绝缘检测仪每日点检记录表',
                                desc: 'XCWI041-F01A',
                                icon: "327318",
                                url: "/mobilemode/appHomepageView.jsp?appHomepageId=111",
                            }]
                        },
                        {
                            category: "敷料",
                            order: "4",
                            forms: [{
                                name: '敷料区物表清洁及消毒记录',
                                desc: 'AHWI028-F05/A1',
                                icon: "327318",
                                url: "/mobilemode/appHomepageView.jsp?appHomepageId=104",
                            }]
                        },
                        {
                            category: "灭菌",
                            order: "5",
                            forms: [{
                                name: '环氧乙烷气罐使用登记表',
                                desc: 'XCWI037-F02A',
                                icon: "327318",
                                url: "/mobilemode/appHomepageView.jsp?appHomepageId=138",
                            }]
                        }
                    ]

                },
                    {
                        key: '002',
                        name: '质量',
                        order: "2",
                        datas: [{
                            category: "每日填写",
                            order: "1",
                            forms: [{
                                name: '手持式ATP测试记录',
                                desc: 'AHWI021-F04B',
                                icon: "327318",
                                url: "/mobilemode/appHomepageView.jsp?appHomepageId=67",
                            }]
                        },
                            {
                                category: "每周填写",
                                order: "2",
                                forms: [{
                                    name: '纯化水质量检测记录（申博）',
                                    desc: 'AHWI021-F02-02 A0',
                                    icon: "327318",
                                    url: "/mobilemode/appHomepageView.jsp?appHomepageId=174",
                                }]
                            },
                            {
                                category: "每月填写",
                                order: "3",
                                forms: [{
                                    name: '紫外线灯测试卡记录',
                                    desc: 'AHWI028－F15A',
                                    icon: "327318",
                                    url: "/mobilemode/appHomepageView.jsp?appHomepageId=165",
                                }]
                            }
                        ]
                    }],

            },
        }
    }

    componentDidMount() {
        this.loadSearchHistory();
    }

    // 加载搜索历史
    loadSearchHistory = () => {
        try {
            const history = localStorage.getItem('searchHistory');
            if (history) {
                this.setState({searchHistory: JSON.parse(history)});
            }
        } catch (error) {
            console.error('加载搜索历史失败:', error);
        }
    }

    // 保存搜索历史
    saveSearchHistory = (keyword) => {
        try {
            let history = [...this.state.searchHistory];
            // 去重：如果关键词已存在，先删除
            const index = history.indexOf(keyword);
            if (index > -1) {
                history.splice(index, 1);
            }
            // 新记录插入到最前面
            history.unshift(keyword);
            // 只保留最近3条
            if (history.length > 3) {
                history = history.slice(0, 3);
            }

            this.setState({searchHistory: history});
            localStorage.setItem('searchHistory', JSON.stringify(history));
        } catch (error) {
            console.error('保存搜索历史失败:', error);
        }
    }

    // 清除搜索历史
    clearSearchHistory = () => {
        this.setState({searchHistory: []});
        localStorage.removeItem('searchHistory');
    }

    // 搜索处理
    handleSearch = (value) => {
        this.setState({searchKeyword: value});
    }

    // Tab切换
    handleTabChange = (tab) => {
        this.setState({activeTab: tab});
    }

    // 表单卡片点击
    handleFormClick = (form) => {
        // 保存到搜索历史
        this.saveSearchHistory(form.name);

        // 这里可以添加导航到具体表单的逻辑
        console.log('点击表单:', form.name);
        // 示例：跳转到表单页面
        // window.location.href = `/form/${form.id}`;
    }

    // 历史记录点击
    handleHistoryClick = (keyword) => {
        this.setState({searchKeyword: keyword});
    }

    // 获取过滤后的表单数据
    getFilteredForms = () => {
        const {allForms, activeTab, searchKeyword} = this.state;
        let filtered = allForms.filter(form => form.tab === activeTab);

        if (searchKeyword) {
            const keyword = searchKeyword.toLowerCase();
            filtered = filtered.filter(form =>
                form.name.toLowerCase().includes(keyword) ||
                form.desc.toLowerCase().includes(keyword)
            );
        }

        return filtered;
    }

    // 按分类分组表单
    getGroupedForms = () => {
        const filtered = this.getFilteredForms();
        const grouped = {};

        filtered.forEach(form => {
            if (!grouped[form.category]) {
                grouped[form.category] = [];
            }
            grouped[form.category].push(form);
        });

        return grouped;
    }

    // 渲染搜索栏
    renderSearchBar = () => {
        const {searchKeyword, searchHistory} = this.state;

        return (
            <div className="search-bar">
                <div className="search-box">
                    <span className="search-icon"><i className="icon-coms-search"/></span>
                    <input
                        type="text"
                        placeholder="搜索表单名称或关键词..."
                        value={searchKeyword}
                        onChange={(e) => this.handleSearch(e.target.value)}
                        autoComplete="off"
                    />
                </div>

                {/* 搜索历史 */}
                <div className="search-history-section">
                    <div className="history-header">
                        <span className="history-title">历史搜索</span>
                        {searchHistory.length > 0 && (
                            <button
                                onClick={this.clearSearchHistory}
                                className="clear-all-btn"
                            >
                                🗑️
                            </button>
                        )}
                    </div>

                    <div className="history-tags">
                        {searchHistory.length > 0 ? (
                            <div className="tags-list">
                                {searchHistory.map((keyword, index) => (
                                    <span
                                        key={index}
                                        className="history-tag"
                                        onClick={() => this.handleHistoryClick(keyword)}
                                    >
                                        {keyword}
                                    </span>
                                ))}
                            </div>
                        ) : (
                            <div className="no-history-tip">暂无历史搜索</div>
                        )}
                    </div>
                </div>
            </div>
        );
    }

    // 渲染Tab切换
    renderTabs = () => {
        const {activeTab} = this.state;

        return (
            <div className="tab-container">
                <div
                    className={`tab-item ${activeTab === 'operation' ? 'active' : ''}`}
                    onClick={() => this.handleTabChange('operation')}
                >
                    运营
                </div>
                <div
                    className={`tab-item ${activeTab === 'quality' ? 'active' : ''}`}
                    onClick={() => this.handleTabChange('quality')}
                >
                    质量
                </div>
            </div>
        );
    }

    // 渲染表单卡片
    renderFormCard = (form, index) => {
        let imgSrc = "";
        if (form.fileid) {
            imgSrc = "/weaver/weaver.file.FileDownload?fileid=" + form.fileid
        } else {
            imgSrc = "/cloudstore/release/${appId}/resources/normal_form.png";
        }

        return (
            <div
                key={index}
                className="form-card"
                onClick={() => this.handleFormClick(form)}
            >
                <div className="form-icon">
                    <img style={{height: '25px'}} src={imgSrc}/>
                </div>


                <h3 className="form-name">{form.name}</h3>
                <p className="form-desc">{form.desc}</p>
            </div>
        );
    }

    // 渲染表单列表
    renderFormList = () => {
        const {searchKeyword} = this.state;
        const groupedForms = this.getGroupedForms();
        const categories = Object.keys(groupedForms);

        if (searchKeyword && categories.length === 0) {
            return (
                <div className="search-matched">
                    <div className="search-matched-title">搜索结果</div>
                    <div className="form-list">
                        <div className="no-data">没有找到匹配的表单</div>
                    </div>
                </div>
            );
        }

        return (
            <div className="original-forms">
                {categories.map(category => (
                    <div key={category}>
                        <h2 className="category-title">{category}</h2>
                        <div className="form-list">
                            {groupedForms[category].map((form, index) =>
                                this.renderFormCard(form, `${category}-${index}`)
                            )}
                        </div>
                    </div>
                ))}
            </div>
        );
    }

    render() {
        return (
            <div className="SD_Page">
                <div className="container">
                    {this.renderSearchBar()}
                    {this.renderTabs()}
                    {this.renderFormList()}
                </div>
            </div>
        )
    }
}
