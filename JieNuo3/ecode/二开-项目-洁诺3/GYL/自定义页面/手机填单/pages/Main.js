// 如果需要使用antd组件，可以在这里导入
// const { SearchBar, List, Button, Icon } = antd;

class Main extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            searchKeyword: '',
            activeTab: '', // 初始为空，将在componentDidMount中设置
            searchHistory: [],
            datas: {
                tabs2: [{
                    key: '001',
                    name: '运营',
                    order: "1",
                    datas: [{
                        category: "多区域通用",
                        order: "1",
                        forms: [{
                            name: '医用封口机检查测试记录表',
                            desc: 'XCWI034-F01A',
                            icon: "327318",
                            url: "/mobilemode/appHomepageView.jsp?appHomepageId=135",
                        }]
                    },
                        {
                            category: "去污",
                            order: "2",
                            forms: [{
                                name: '清洗消毒器',
                                desc: '清洗消毒器使用记录表',
                                icon: "327318",
                                url: "/mobilemode/appHomepageView.jsp?appHomepageId=54",
                            },
                                {
                                    name: '去污区物表清洁及消毒记录',
                                    desc: '去污区物体表面清洁消毒记录表',
                                    icon: "327318",
                                    url: "/mobilemode/appHomepageView.jsp?appHomepageId=57",
                                },
                                {
                                    name: '（酒精）危化品使用登记表',
                                    desc: '（酒精）危化品使用登记表',
                                    icon: "327318",
                                    url: "/mobilemode/appHomepageView.jsp?appHomepageId=60",
                                },
                                {
                                    name: '超声清洗机检查及配液配置记录表',
                                    desc: 'XCWI030-F01A1',
                                    icon: "327318",
                                    url: "/mobilemode/appHomepageView.jsp?appHomepageId=95",
                                },]
                        },
                        {
                            category: "检查包装",
                            order: "3",
                            forms: [{
                                name: '绝缘检测仪每日点检记录表',
                                desc: 'XCWI041-F01A',
                                icon: "327318",
                                url: "/mobilemode/appHomepageView.jsp?appHomepageId=111",
                            }]
                        },
                        {
                            category: "敷料",
                            order: "4",
                            forms: [{
                                name: '敷料区物表清洁及消毒记录',
                                desc: 'AHWI028-F05/A1',
                                icon: "327318",
                                url: "/mobilemode/appHomepageView.jsp?appHomepageId=104",
                            }]
                        },
                        {
                            category: "灭菌",
                            order: "5",
                            forms: [{
                                name: '环氧乙烷气罐使用登记表',
                                desc: 'XCWI037-F02A',
                                icon: "327318",
                                url: "/mobilemode/appHomepageView.jsp?appHomepageId=138",
                            }]
                        }
                    ]

                },
                    {
                        key: '002',
                        name: '质量',
                        order: "2",
                        datas: [{
                            category: "每日填写",
                            order: "1",
                            forms: [{
                                name: '手持式ATP测试记录',
                                desc: 'AHWI021-F04B',
                                icon: "327318",
                                url: "/mobilemode/appHomepageView.jsp?appHomepageId=67",
                            }]
                        },
                            {
                                category: "每周填写",
                                order: "2",
                                forms: [{
                                    name: '纯化水质量检测记录（申博）',
                                    desc: 'AHWI021-F02-02 A0',
                                    icon: "327318",
                                    url: "/mobilemode/appHomepageView.jsp?appHomepageId=174",
                                }]
                            },
                            {
                                category: "每月填写",
                                order: "3",
                                forms: [{
                                    name: '紫外线灯测试卡记录',
                                    desc: 'AHWI028－F15A',
                                    icon: "327318",
                                    url: "/mobilemode/appHomepageView.jsp?appHomepageId=165",
                                }]
                            }
                        ]
                    }],

            },
        }
    }

    componentDidMount() {
        this.loadSearchHistory();
        this.initializeActiveTab();
    }

    // 初始化活动Tab
    initializeActiveTab = () => {
        const {datas} = this.state;
        if (datas.tabs && datas.tabs.length > 0) {
            // 按order排序，选择第一个tab作为默认激活tab
            const sortedTabs = [...datas.tabs].sort((a, b) => parseInt(a.order) - parseInt(b.order));
            this.setState({activeTab: sortedTabs[0].key});
        }
    }

    // 加载搜索历史
    loadSearchHistory = () => {
        try {
            const history = localStorage.getItem('searchHistory');
            if (history) {
                this.setState({searchHistory: JSON.parse(history)});
            }
        } catch (error) {
            console.error('加载搜索历史失败:', error);
        }
    }

    // 保存搜索历史
    saveSearchHistory = (keyword) => {
        try {
            let history = [...this.state.searchHistory];
            // 去重：如果关键词已存在，先删除
            const index = history.indexOf(keyword);
            if (index > -1) {
                history.splice(index, 1);
            }
            // 新记录插入到最前面
            history.unshift(keyword);
            // 只保留最近3条
            if (history.length > 3) {
                history = history.slice(0, 3);
            }

            this.setState({searchHistory: history});
            localStorage.setItem('searchHistory', JSON.stringify(history));
        } catch (error) {
            console.error('保存搜索历史失败:', error);
        }
    }

    // 清除搜索历史
    clearSearchHistory = () => {
        this.setState({searchHistory: []});
        localStorage.removeItem('searchHistory');
    }

    // 搜索处理
    handleSearch = (value) => {
        this.setState({searchKeyword: value});
    }

    // Tab切换
    handleTabChange = (tab) => {
        this.setState({activeTab: tab});
    }

    // 表单卡片点击
    handleFormClick = (form) => {
        // 保存到搜索历史
        this.saveSearchHistory(form.name);

        // 跳转到表单页面
        if (form.url) {
            if (typeof $u === 'function') {
                $u(form.url);
            } else {
                window.location.href = form.url;
            }
        } else {
            console.log('点击表单:', form.name, '但没有配置URL');
        }
    }

    // 历史记录点击
    handleHistoryClick = (keyword) => {
        this.setState({searchKeyword: keyword});
    }

    // 获取当前活动Tab的数据
    getCurrentTabData = () => {
        const {datas, activeTab} = this.state;
        if (!datas.tabs || !activeTab) return null;

        return datas.tabs.find(tab => tab.key === activeTab);
    }

    // 获取过滤后的表单数据
    getFilteredForms = () => {
        const {searchKeyword} = this.state;
        const currentTab = this.getCurrentTabData();

        if (!currentTab || !currentTab.datas) return [];

        let allForms = [];
        // 展开所有分类下的表单
        currentTab.datas.forEach(categoryData => {
            if (categoryData.forms) {
                categoryData.forms.forEach(form => {
                    allForms.push({
                        ...form,
                        category: categoryData.category,
                        categoryOrder: categoryData.order
                    });
                });
            }
        });

        // 如果有搜索关键词，进行过滤
        if (searchKeyword) {
            const keyword = searchKeyword.toLowerCase();
            allForms = allForms.filter(form =>
                form.name.toLowerCase().includes(keyword) ||
                form.desc.toLowerCase().includes(keyword)
            );
        }

        return allForms;
    }

    // 按分类分组表单
    getGroupedForms = () => {
        const filtered = this.getFilteredForms();
        const grouped = {};

        filtered.forEach(form => {
            if (!grouped[form.category]) {
                grouped[form.category] = {
                    forms: [],
                    order: form.categoryOrder
                };
            }
            grouped[form.category].forms.push(form);
        });

        return grouped;
    }

    // 渲染搜索栏
    renderSearchBar = () => {
        const {searchKeyword, searchHistory} = this.state;

        return (
            <div className="search-bar">
                <div className="search-box">
                    <span className="search-icon"><i className="icon-coms-search"/></span>
                    <input
                        type="text"
                        placeholder="搜索表单名称或关键词..."
                        value={searchKeyword}
                        onChange={(e) => this.handleSearch(e.target.value)}
                        autoComplete="off"
                    />
                </div>

                {/* 搜索历史 */}
                <div className="search-history-section">
                    <div className="history-header">
                        <span className="history-title">历史搜索</span>
                        {searchHistory.length > 0 && (
                            <button
                                onClick={this.clearSearchHistory}
                                className="clear-all-btn"
                            >
                                🗑️
                            </button>
                        )}
                    </div>

                    <div className="history-tags">
                        {searchHistory.length > 0 ? (
                            <div className="tags-list">
                                {searchHistory.map((keyword, index) => (
                                    <span
                                        key={index}
                                        className="history-tag"
                                        onClick={() => this.handleHistoryClick(keyword)}
                                    >
                                        {keyword}
                                    </span>
                                ))}
                            </div>
                        ) : (
                            <div className="no-history-tip">暂无历史搜索</div>
                        )}
                    </div>
                </div>
            </div>
        );
    }

    // 渲染Tab切换
    renderTabs = () => {
        const {datas, activeTab} = this.state;

        if (!datas.tabs || datas.tabs.length === 0) {
            return null; // 没有tabs时不渲染tab容器
        }

        // 按order排序tabs
        const sortedTabs = [...datas.tabs].sort((a, b) => parseInt(a.order) - parseInt(b.order));

        return (
            <div className="tab-container">
                {sortedTabs.map(tab => (
                    <div
                        key={tab.key}
                        className={`tab-item ${activeTab === tab.key ? 'active' : ''}`}
                        onClick={() => this.handleTabChange(tab.key)}
                    >
                        {tab.name}
                    </div>
                ))}
            </div>
        );
    }

    // 渲染表单卡片
    renderFormCard = (form, index) => {
        let imgSrc = "";
        if (form.icon) {
            // 如果icon是文件ID，使用文件下载链接
            if (/^\d+$/.test(form.icon)) {
                imgSrc = "/weaver/weaver.file.FileDownload?fileid=" + form.icon;
            } else {
                // 如果icon是其他格式，可能是emoji或其他标识
                imgSrc = "/cloudstore/release/${appId}/resources/normal_form.png";
            }
        } else {
            imgSrc = "/cloudstore/release/${appId}/resources/normal_form.png";
        }

        return (
            <div
                key={index}
                className="form-card"
                onClick={() => this.handleFormClick(form)}
            >
                <div className="form-icon">
                    <img style={{height: '25px', width: '25px', objectFit: 'contain'}} src={imgSrc} alt="表单图标"/>
                </div>
                <h3 className="form-name">{form.name}</h3>
                <p className="form-desc">{form.desc}</p>
            </div>
        );
    }

    // 渲染无权限状态
    renderNoPermission = () => {
        return (
            <div className="no-permission">
                <div className="no-permission-icon">🔒</div>
                <div className="no-permission-title">暂无权限</div>
                <div className="no-permission-desc">您当前没有任何表单的访问权限，请联系管理员</div>
            </div>
        );
    }

    // 渲染表单列表
    renderFormList = () => {
        const {datas, searchKeyword} = this.state;

        // 检查是否有权限（tabs为空或没有数据）
        if (!datas.tabs || datas.tabs.length === 0) {
            return this.renderNoPermission();
        }

        const groupedForms = this.getGroupedForms();
        const categories = Object.keys(groupedForms);

        // 搜索无结果
        if (searchKeyword && categories.length === 0) {
            return (
                <div className="search-matched">
                    <div className="search-matched-title">搜索结果</div>
                    <div className="form-list">
                        <div className="no-data">没有找到匹配的表单</div>
                    </div>
                </div>
            );
        }

        // 当前tab没有数据
        if (categories.length === 0) {
            return (
                <div className="original-forms">
                    <div className="no-data">当前分类暂无表单</div>
                </div>
            );
        }

        // 按分类order排序
        const sortedCategories = categories.sort((a, b) => {
            const orderA = groupedForms[a].order || '999';
            const orderB = groupedForms[b].order || '999';
            return parseInt(orderA) - parseInt(orderB);
        });

        return (
            <div className="original-forms">
                {sortedCategories.map(category => (
                    <div key={category}>
                        <h2 className="category-title">{category}</h2>
                        <div className="form-list">
                            {groupedForms[category].forms.map((form, index) =>
                                this.renderFormCard(form, `${category}-${index}`)
                            )}
                        </div>
                    </div>
                ))}
            </div>
        );
    }

    render() {
        const {datas} = this.state;

        return (
            <div className="SD_Page">
                <div className="container">
                    {/* 只有在有权限时才显示搜索栏 */}
                    {datas.tabs && datas.tabs.length > 0 && this.renderSearchBar()}
                    {this.renderTabs()}
                    {this.renderFormList()}
                </div>
            </div>
        )
    }
}

// 导出组件
window.Main = Main;
