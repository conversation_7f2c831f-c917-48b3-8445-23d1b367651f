/* 基础样式重置 */
.SD_Page * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
    -webkit-tap-highlight-color: transparent;
}

.SD_Page {
    width: 100%;
    height: 100vh;
    background-color: #f0f0f0;
    color: #333;
    line-height: 1.5;
    -webkit-overflow-scrolling: touch;
    overflow: hidden;
}

.container {
    width: 100%;
    height: 100%;
    padding: 0;
    background: #f0f0f0;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}

/* 顶部搜索栏 */
.search-bar {
    position: sticky;
    top: 0;
    z-index: 10;
    background-color: #fff;
    padding: 18px 20px 12px 20px;
    box-shadow: 0 4px 16px rgba(24, 144, 255, 0.08);
}

.search-box {
    display: flex;
    align-items: center;
    background-color: #f1f3f6;
    border-radius: 22px;
    padding: 10px 18px;
    width: 100%;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.04);
}

.search-box input {
    flex: 1;
    border: none;
    background: transparent;
    outline: none;
    font-size: 15px;
    padding: 4px 0;
    -webkit-appearance: none;
    appearance: none;
    user-select: text;
    -webkit-user-select: text;
}

.search-box input:focus {
    outline: none;
}

.search-icon {
    color: #1890ff;
    margin-right: 10px;
    font-size: 18px;
}

/* Tab切换 - 优化设计 */
.tab-container {
    display: flex;
    background-color: #fff;
    position: sticky;
    top: 64px;
    z-index: 20;
    padding: 0 20px;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.06);
    border-radius: 0 0 16px 16px;
    margin-bottom: 8px;
}

.tab-item {
    flex: 1;
    text-align: center;
    padding: 14px 0;
    font-size: 16px;
    font-weight: 500;
    color: #b0b0b0;
    cursor: pointer;
    transition: all 0.3s;
    position: relative;
    border-bottom: 3px solid transparent;
}

.tab-item.active {
    color: #1890ff;
    border-bottom: 3px solid #1890ff;
    background: none;
}

/* 分类标题 */
.category-title {
    font-size: 17px;
    font-weight: 600;
    color: #333;
    margin: 32px 20px 14px 20px;
    display: flex;
    align-items: center;
    letter-spacing: 1px;
}

.category-title:before {
    content: '';
    display: inline-block;
    width: 5px;
    height: 18px;
    background-color: #1890ff;
    margin-right: 10px;
    border-radius: 2px;
}

/* 表单列表 - 缩小卡片并增加圆角 */
.form-list {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    width: 100%;
    margin: 0 0 20px 0;
    padding: 0 12px;
    align-items: stretch;
}

.form-card {
    background-color: #fff;
    border-radius: 16px;
    box-shadow: 0 4px 16px rgba(24, 144, 255, 0.13);
    transition: transform 0.2s, box-shadow 0.2s;
    cursor: pointer;
    min-width: 0;
    word-break: break-all;
    font-size: 13px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    aspect-ratio: 1 / 0.8;
    padding: 12px 8px;
    justify-content: flex-start;
}

.form-card:hover {
    transform: translateY(-3px) scale(1.03);
    box-shadow: 0 6px 18px rgba(24, 144, 255, 0.13);
}

.form-icon {
    font-size: 16px;
    width: 24px;
    height: 24px;
    background-color: #e6f7ff;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 6px;
    color: #1890ff;
}

.form-name {
    font-size: 15px;
    font-weight: 600;
    margin-bottom: 6px;
    color: #222;
}

.form-desc {
    font-size: 13px;
    color: #999;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

/* 暂无数据样式 - 添加白色背景并居中 */
.no-data {
    text-align: center;
    padding: 28px 0;
    color: #b0b0b0;
    font-size: 15px;
    background-color: #fff;
    border-radius: 14px;
    grid-column: 1 / -1;
    margin: 0;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.05);
}

/* 搜索匹配结果样式 */
.search-matched {
    display: block;
    background-color: #fff;
    border-radius: 12px;
    margin: 16px 20px;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.07);
}

.search-matched-title {
    font-size: 17px;
    font-weight: 600;
    color: #333;
    padding: 18px 18px 10px 18px;
    border-bottom: 1px solid #f0f0f0;
}

.search-history-section {
    margin-top: 12px;
    background-color: #fff;
    border-radius: 8px;
    padding: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* 头部：标题（历史搜索）+ 删除按钮 左右分布 */
.history-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
}

.history-title {
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

/* 删除全部按钮 */
.clear-all-btn {
    background: transparent !important;
    border: none !important;
    cursor: pointer;
    font-size: 18px;
    color: #999;
    transition: color 0.2s;
    padding: 0 !important;
    height: auto !important;
    line-height: 1 !important;
}

.clear-all-btn:hover {
    color: #ff4d4f !important;
}

/* 标签列表：自动换行 + 间距 */
.tags-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

/* 单个历史标签：圆角 + 浅灰边框 + 悬浮高亮 */
.history-tag {
    padding: 4px 10px;
    border: 1px solid #e5e5e5;
    border-radius: 18px;
    color: #666;
    font-size: 12px;
    cursor: pointer;
    transition: background-color 0.2s, color 0.2s;
    white-space: nowrap;
}

.history-tag:hover {
    background-color: #fafafa;
    color: #333;
}

/* 无历史时的提示 */
.no-history-tip {
    color: #999;
    font-size: 12px;
}

/* 原始表单容器 */
.original-forms {
    width: 100%;
}

/* 响应式调整 */
@media (max-width: 500px) {
    .form-list {
        grid-template-columns: 1fr;
    }

    .search-bar,
    .tab-container,
    .category-title,
    .form-list,
    .search-matched {
        margin-left: 0;
        margin-right: 0;
        padding-left: 8px;
        padding-right: 8px;
    }
}

/* antd组件样式覆盖 */
.am-search-bar {
    background: transparent !important;
    border: none !important;
}

.am-search-bar .am-search-bar-input {
    background: transparent !important;
    border: none !important;
    font-size: 15px !important;
}

.am-search-bar .am-search-bar-input input {
    background: transparent !important;
    border: none !important;
    outline: none !important;
    font-size: 15px !important;
    padding: 4px 0 !important;
}